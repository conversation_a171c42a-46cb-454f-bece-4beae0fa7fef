import React, { useState, useEffect } from 'react';
import axios from 'axios';

export const BalanceSheet = () => {
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [balanceSheetData, setBalanceSheetData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showStatement, setShowStatement] = useState(false);

  // Set default dates on component mount
  useEffect(() => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const januaryFirst = `${currentYear}-01-01`;
    const today = currentDate.toISOString().split('T')[0];

    setFromDate(januaryFirst);
    setToDate(today);
  }, []);

  const handleGenerateStatement = async () => {
    if (!fromDate || !toDate) {
      setError('Please select both from and to dates');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await axios.post('/api/balance-sheet', {
        from_date: fromDate,
        to_date: toDate,
      });

      if (response.data.success) {
        setBalanceSheetData(response.data.data);
        setShowStatement(true);
      } else {
        throw new Error(response.data.message || 'Failed to generate balance sheet');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to generate balance sheet. Please try again.');
      console.error('Error generating balance sheet:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePrintStatement = () => {
    if (!balanceSheetData) return;

    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  const generatePrintContent = () => {
    if (!balanceSheetData) return '';

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Balance Sheet</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .date-range { text-align: center; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .category-header { background-color: #e8f4f8; font-weight: bold; }
            .total-row { background-color: #f0f0f0; font-weight: bold; }
            .amount { text-align: right; }
            .grand-total { background-color: #d4edda; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Balance Sheet</h1>
          </div>
          <div class="date-range">
            <p>From: ${fromDate} To: ${toDate}</p>
          </div>
          ${generateBalanceSheetTable()}
        </body>
      </html>
    `;
  };

  const generateBalanceSheetTable = () => {
    if (!balanceSheetData) return '';

    const { assets, liabilities, equity, totals } = balanceSheetData;

    return `
      <table>
        <thead>
          <tr>
            <th style="width: 60%">Category</th>
            <th style="width: 20%">Amount</th>
            <th style="width: 20%">Total</th>
          </tr>
        </thead>
        <tbody>
          <!-- ASSETS -->
          <tr class="category-header">
            <td colspan="3">ASSETS</td>
          </tr>
          <tr>
            <td>&nbsp;&nbsp;Cash in Hand</td>
            <td class="amount">${assets.cashInHand.toLocaleString()}</td>
            <td></td>
          </tr>
          <tr>
            <td>&nbsp;&nbsp;Bank Accounts</td>
            <td class="amount">${assets.bankAccounts.toLocaleString()}</td>
            <td></td>
          </tr>
          <tr>
            <td>&nbsp;&nbsp;Inventory</td>
            <td class="amount">${assets.inventory.toLocaleString()}</td>
            <td></td>
          </tr>
          <tr>
            <td>&nbsp;&nbsp;Account Receivable</td>
            <td class="amount">${assets.accountReceivable.toLocaleString()}</td>
            <td></td>
          </tr>
          <tr class="total-row">
            <td><strong>Total Assets</strong></td>
            <td></td>
            <td class="amount"><strong>${totals.totalAssets.toLocaleString()}</strong></td>
          </tr>

          <!-- LIABILITIES -->
          <tr class="category-header">
            <td colspan="3">LIABILITIES</td>
          </tr>
          <tr>
            <td>&nbsp;&nbsp;Account Payables</td>
            <td class="amount">${liabilities.accountPayables.toLocaleString()}</td>
            <td></td>
          </tr>
          <tr>
            <td>&nbsp;&nbsp;Loan</td>
            <td class="amount">${liabilities.loan.toLocaleString()}</td>
            <td></td>
          </tr>
          <tr class="total-row">
            <td><strong>Total Liabilities</strong></td>
            <td></td>
            <td class="amount"><strong>${totals.totalLiabilities.toLocaleString()}</strong></td>
          </tr>

          <!-- OWNER'S EQUITY -->
          <tr class="category-header">
            <td colspan="3">OWNER'S EQUITY</td>
          </tr>
          <tr>
            <td>&nbsp;&nbsp;Capital Introduced</td>
            <td class="amount">${equity.capitalIntroduced.toLocaleString()}</td>
            <td></td>
          </tr>
          <tr>
            <td>&nbsp;&nbsp;Net Profit</td>
            <td class="amount">${equity.netProfit.toLocaleString()}</td>
            <td></td>
          </tr>
          <tr>
            <td>&nbsp;&nbsp;Drawing</td>
            <td class="amount">${equity.drawing.toLocaleString()}</td>
            <td></td>
          </tr>
          <tr class="total-row">
            <td><strong>Total Equity</strong></td>
            <td></td>
            <td class="amount"><strong>${totals.totalEquity.toLocaleString()}</strong></td>
          </tr>

          <!-- GRAND TOTAL -->
          <tr class="grand-total">
            <td><strong>Total Liabilities + Equity</strong></td>
            <td></td>
            <td class="amount"><strong>${totals.totalLiabilitiesAndEquity.toLocaleString()}</strong></td>
          </tr>
        </tbody>
      </table>
    `;
  };

  return (
    <div className="container p-6 mx-auto">
      <div className="p-6 bg-white rounded-lg shadow-md">
        <h1 className="mb-6 text-2xl font-bold text-gray-800">Balance Sheet</h1>

        {/* Date Selection */}
        <div className="grid grid-cols-1 gap-4 mb-6 md:grid-cols-3">
          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              From Date
            </label>
            <input
              type="date"
              value={fromDate}
              onChange={(e) => setFromDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              To Date
            </label>
            <input
              type="date"
              value={toDate}
              onChange={(e) => setToDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex items-end">
            <button
              onClick={handleGenerateStatement}
              disabled={loading}
              className="w-full px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {loading ? 'Generating...' : 'Generate Statement'}
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="px-4 py-3 mb-4 text-red-700 bg-red-100 border border-red-400 rounded">
            {error}
          </div>
        )}

        {/* Print Button */}
        {showStatement && balanceSheetData && (
          <div className="mb-4">
            <button
              onClick={handlePrintStatement}
              className="px-4 py-2 text-white bg-green-600 rounded-md hover:bg-green-700"
            >
              Print Statement
            </button>
          </div>
        )}

        {/* Balance Sheet Display */}
        {showStatement && balanceSheetData && (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase border-b">
                    Category
                  </th>
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase border-b">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase border-b">
                    Total
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {/* ASSETS */}
                <tr className="bg-blue-50">
                  <td colSpan="3" className="px-6 py-3 font-bold text-gray-900">
                    ASSETS
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                    &nbsp;&nbsp;Cash in Hand
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.assets.cashInHand.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                    &nbsp;&nbsp;Bank Accounts
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.assets.bankAccounts.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                    &nbsp;&nbsp;Inventory
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.assets.inventory.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                    &nbsp;&nbsp;Account Receivable
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.assets.accountReceivable.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                </tr>
                <tr className="bg-gray-100">
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 whitespace-nowrap">
                    Total Assets
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.totals.totalAssets.toLocaleString()}
                  </td>
                </tr>

                {/* LIABILITIES */}
                <tr className="bg-red-50">
                  <td colSpan="3" className="px-6 py-3 font-bold text-gray-900">
                    LIABILITIES
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                    &nbsp;&nbsp;Account Payables
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.liabilities.accountPayables.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                    &nbsp;&nbsp;Loan
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.liabilities.loan.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                </tr>
                <tr className="bg-gray-100">
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 whitespace-nowrap">
                    Total Liabilities
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.totals.totalLiabilities.toLocaleString()}
                  </td>
                </tr>

                {/* OWNER'S EQUITY */}
                <tr className="bg-green-50">
                  <td colSpan="3" className="px-6 py-3 font-bold text-gray-900">
                    OWNER'S EQUITY
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                    &nbsp;&nbsp;Capital Introduced
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.equity.capitalIntroduced.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                    &nbsp;&nbsp;Net Profit
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.equity.netProfit.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                    &nbsp;&nbsp;Drawing
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.equity.drawing.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                </tr>
                <tr className="bg-gray-100">
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 whitespace-nowrap">
                    Total Equity
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.totals.totalEquity.toLocaleString()}
                  </td>
                </tr>

                {/* GRAND TOTAL */}
                <tr className="bg-yellow-100">
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 whitespace-nowrap">
                    Total Liabilities + Equity
                  </td>
                  <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                    -
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-right text-gray-900 whitespace-nowrap">
                    {balanceSheetData.totals.totalLiabilitiesAndEquity.toLocaleString()}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};
