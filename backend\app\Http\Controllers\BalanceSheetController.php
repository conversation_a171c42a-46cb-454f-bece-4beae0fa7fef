<?php

namespace App\Http\Controllers;

use App\Models\StaffLedger;
use App\Models\Payment;
use App\Models\AccountSubGroup;
use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class BalanceSheetController extends Controller
{
    public function generateBalanceSheet(Request $request)
    {
        $request->validate([
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
        ]);

        $fromDate = Carbon::parse($request->from_date)->startOfDay();
        $toDate = Carbon::parse($request->to_date)->endOfDay();

        try {
            // ASSETS
            $assets = [
                'cashInHand' => $this->getCashInHandBalance($fromDate, $toDate),
                'bankAccounts' => $this->getBankAccountBalance($fromDate, $toDate),
                'inventory' => $this->getInventoryValue($fromDate, $toDate),
                'accountReceivable' => $this->getAccountReceivableBalance($fromDate, $toDate),
            ];

            // LIABILITIES
            $liabilities = [
                'accountPayables' => $this->getAccountPayableBalance($fromDate, $toDate),
                'loan' => $this->getLoanBalance($fromDate, $toDate),
            ];

            // OWNER'S EQUITY
            $equity = [
                'capitalIntroduced' => $this->getCapitalBalance($fromDate, $toDate),
                'netProfit' => $this->getNetProfitBalance($fromDate, $toDate),
                'drawing' => $this->getDrawingBalance($fromDate, $toDate),
            ];

            // Calculate totals
            $totalAssets = array_sum($assets);
            $totalLiabilities = array_sum($liabilities);
            $totalEquity = array_sum($equity);
            $totalLiabilitiesAndEquity = $totalLiabilities + $totalEquity;

            $totals = [
                'totalAssets' => $totalAssets,
                'totalLiabilities' => $totalLiabilities,
                'totalEquity' => $totalEquity,
                'totalLiabilitiesAndEquity' => $totalLiabilitiesAndEquity,
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'assets' => $assets,
                    'liabilities' => $liabilities,
                    'equity' => $equity,
                    'totals' => $totals,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating balance sheet: ' . $e->getMessage(),
            ], 500);
        }
    }

    private function getCashInHandBalance($fromDate, $toDate)
    {
        // Get opening balance for Cash in Hand and its subgroups
        $subGroups = AccountSubGroup::where('main_group', 'Cash in Hand')->pluck('sub_group_name')->toArray();
        $groups = array_merge(['Cash in Hand'], $subGroups);
        $openingBalance = StaffLedger::whereIn('account_group', $groups)->sum('opening_balance');

        // Get cash transactions from payments (receive vouchers add, payment vouchers subtract)
        $receiveVouchers = Payment::where('payment_method', 'cash')
            ->where('voucher_no', 'like', 'REC-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        $paymentVouchers = Payment::where('payment_method', 'cash')
            ->where('voucher_no', 'like', 'PAY-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        return $openingBalance + $receiveVouchers - $paymentVouchers;
    }

    private function getBankAccountBalance($fromDate, $toDate)
    {
        // Get opening balance for Bank Accounts and its subgroups
        $subGroups = AccountSubGroup::where('main_group', 'Bank Accounts')->pluck('sub_group_name')->toArray();
        $groups = array_merge(['Bank Accounts'], $subGroups);
        $openingBalance = StaffLedger::whereIn('account_group', $groups)->sum('opening_balance');

        // Get bank transactions from payments
        $receiveVouchers = Payment::whereIn('payment_method', ['bank', 'card', 'cheque'])
            ->where('voucher_no', 'like', 'REC-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        $paymentVouchers = Payment::whereIn('payment_method', ['bank', 'card', 'cheque'])
            ->where('voucher_no', 'like', 'PAY-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        return $openingBalance + $receiveVouchers - $paymentVouchers;
    }

    private function getInventoryValue($fromDate, $toDate)
    {
        // Calculate total stock value at cost price
        $totalStockValue = 0;

        // Get products with variants
        $products = Product::with('variants')->get();

        foreach ($products as $product) {
            if ($product->variants && $product->variants->count() > 0) {
                foreach ($product->variants as $variant) {
                    $closingStock = $this->calculateClosingStock($variant, $toDate);
                    $costPrice = $variant->buying_cost ?? $product->buying_cost ?? 0;
                    $totalStockValue += $closingStock * $costPrice;
                }
            } else {
                $closingStock = $this->calculateClosingStockForProduct($product, $toDate);
                $costPrice = $product->buying_cost ?? 0;
                $totalStockValue += $closingStock * $costPrice;
            }
        }

        return $totalStockValue;
    }

    private function calculateClosingStock($variant, $toDate)
    {
        $openingStock = $variant->opening_stock_quantity ?? 0;

        // Calculate purchased quantity up to the date
        $purchased = DB::table('purchase_items')
            ->join('purchases', 'purchase_items.purchase_id', '=', 'purchases.id')
            ->where('purchase_items.product_variant_id', $variant->product_variant_id)
            ->where('purchases.date_of_purchase', '<=', $toDate)
            ->sum('purchase_items.quantity') ?? 0;

        // Calculate sold quantity up to the date (from invoices and sales)
        $soldInvoice = DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->where('invoice_items.product_variant_id', $variant->product_variant_id)
            ->where('invoices.invoice_date', '<=', $toDate)
            ->whereNull('invoices.deleted_at')
            ->sum(DB::raw('invoice_items.quantity + IFNULL(invoice_items.free, 0)')) ?? 0;

        $soldPOS = DB::table('sale_items')
            ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
            ->where('sale_items.product_variant_id', $variant->product_variant_id)
            ->where('sales.created_at', '<=', $toDate)
            ->whereNull('sales.deleted_at')
            ->sum(DB::raw('sale_items.quantity + IFNULL(sale_items.free_qty, 0)')) ?? 0;

        $sold = $soldInvoice + $soldPOS;
        $closingStock = $openingStock + $purchased - $sold;

        return max(0, $closingStock);
    }

    private function calculateClosingStockForProduct($product, $toDate)
    {
        $openingStock = $product->opening_stock_quantity ?? 0;

        // Calculate purchased quantity up to the date
        $purchased = DB::table('purchase_items')
            ->join('purchases', 'purchase_items.purchase_id', '=', 'purchases.id')
            ->where('purchase_items.product_id', $product->product_id)
            ->where('purchases.date_of_purchase', '<=', $toDate)
            ->sum('purchase_items.quantity') ?? 0;

        // Calculate sold quantity up to the date
        $soldInvoice = DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->where('invoice_items.product_id', $product->product_id)
            ->where('invoices.invoice_date', '<=', $toDate)
            ->whereNull('invoices.deleted_at')
            ->sum(DB::raw('invoice_items.quantity + IFNULL(invoice_items.free, 0)')) ?? 0;

        $soldPOS = DB::table('sale_items')
            ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
            ->where('sale_items.product_id', $product->product_id)
            ->where('sales.created_at', '<=', $toDate)
            ->whereNull('sales.deleted_at')
            ->sum(DB::raw('sale_items.quantity + IFNULL(sale_items.free_qty, 0)')) ?? 0;

        $sold = $soldInvoice + $soldPOS;
        $closingStock = $openingStock + $purchased - $sold;

        return max(0, $closingStock);
    }

    private function getAccountReceivableBalance($fromDate, $toDate)
    {
        // Get total outstanding amount from outstanding controller logic
        // For balance sheet, we want all outstanding amounts up to the end date
        $response = app(\App\Http\Controllers\OutstandingController::class)->index(new Request());
        $responseData = json_decode($response->getContent(), true);

        // Suppress unused parameter warnings - using controller method that doesn't filter by date
        unset($fromDate, $toDate);
        return $responseData['total_pending_amount'] ?? 0;
    }

    private function getAccountPayableBalance($fromDate, $toDate)
    {
        // Get total payable amount from payable controller logic
        // For balance sheet, we want all payable amounts up to the end date
        $response = app(\App\Http\Controllers\PayableController::class)->index(new Request());
        $responseData = json_decode($response->getContent(), true);

        // Suppress unused parameter warnings - using controller method that doesn't filter by date
        unset($fromDate, $toDate);
        return $responseData['total_pending_amount'] ?? 0;
    }

    private function getLoanBalance($fromDate, $toDate)
    {
        // Get opening balance for Loan Liabilities and its subgroups
        $subGroups = AccountSubGroup::where('main_group', 'Loan Liabilities')->pluck('sub_group_name')->toArray();
        $groups = array_merge(['Loan Liabilities'], $subGroups);
        $openingBalance = StaffLedger::whereIn('account_group', $groups)->sum('opening_balance');

        // Get loan-related transactions from payments
        $loanLedgerIds = StaffLedger::whereIn('account_group', $groups)->pluck('id')->toArray();

        $receiveVouchers = Payment::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $loanLedgerIds)
            ->where('voucher_no', 'like', 'REC-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        $paymentVouchers = Payment::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $loanLedgerIds)
            ->where('voucher_no', 'like', 'PAY-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        return $openingBalance + $receiveVouchers - $paymentVouchers;
    }

    private function getCapitalBalance($fromDate, $toDate)
    {
        // Get opening balance for Capital Account and its subgroups
        $subGroups = AccountSubGroup::where('main_group', 'Capital Account')->pluck('sub_group_name')->toArray();
        $groups = array_merge(['Capital Account'], $subGroups);
        $openingBalance = StaffLedger::whereIn('account_group', $groups)->sum('opening_balance');

        // Get capital-related transactions from payments
        $capitalLedgerIds = StaffLedger::whereIn('account_group', $groups)->pluck('id')->toArray();

        $receiveVouchers = Payment::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $capitalLedgerIds)
            ->where('voucher_no', 'like', 'REC-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        $paymentVouchers = Payment::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $capitalLedgerIds)
            ->where('voucher_no', 'like', 'PAY-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        return $openingBalance + $receiveVouchers - $paymentVouchers;
    }

    private function getNetProfitBalance($fromDate, $toDate)
    {
        // Get net profit from ProfitAndLossReportController
        $request = new Request([
            'from_date' => $fromDate->format('Y-m-d'),
            'to_date' => $toDate->format('Y-m-d'),
        ]);
        
        $response = app(\App\Http\Controllers\ProfitAndLossReportController::class)->generateProfitAndLoss($request);
        $responseData = json_decode($response->getContent(), true);
        
        return $responseData['totals']['netProfitLoss'] ?? 0;
    }

    private function getDrawingBalance($fromDate, $toDate)
    {
        // Drawing is when owner takes money out - this would be in payment vouchers for owner/drawing accounts
        // For now, we'll return 0 as drawing accounts need to be specifically identified
        // TODO: Implement drawing calculation based on owner withdrawal accounts
        // Suppress unused parameter warnings for future implementation
        unset($fromDate, $toDate);
        return 0;
    }
}
