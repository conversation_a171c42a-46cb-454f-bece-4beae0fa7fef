<?php

namespace App\Http\Controllers;

use App\Models\StaffLedger;
use App\Models\Payment;
use App\Models\AccountSubGroup;
use App\Models\ChequeStatement;
use Illuminate\Http\Request;
use Carbon\Carbon;

class BalanceSheetController extends Controller
{
    public function generateBalanceSheet(Request $request)
    {
        $request->validate([
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
        ]);

        $fromDate = Carbon::parse($request->from_date)->startOfDay();
        $toDate = Carbon::parse($request->to_date)->endOfDay();

        try {
            // ASSETS
            $assets = [
                'cashInHand' => $this->getCashInHandBalance($fromDate, $toDate),
                'bankAccounts' => $this->getBankAccountBalance($fromDate, $toDate),
                'inventory' => $this->getInventoryValue($fromDate, $toDate),
                'accountReceivable' => $this->getAccountReceivableBalance($fromDate, $toDate),
            ];

            // LIABILITIES
            $liabilities = [
                'accountPayables' => $this->getAccountPayableBalance($fromDate, $toDate),
                'loan' => $this->getLoanBalance($fromDate, $toDate),
            ];

            // OWNER'S EQUITY
            $equity = [
                'capitalIntroduced' => $this->getCapitalBalance($fromDate, $toDate),
                'netProfit' => $this->getNetProfitBalance($fromDate, $toDate),
                'drawing' => $this->getDrawingBalance($fromDate, $toDate),
            ];

            // Calculate totals
            $totalAssets = array_sum($assets);
            $totalLiabilities = array_sum($liabilities);
            $totalEquity = array_sum($equity);
            $totalLiabilitiesAndEquity = $totalLiabilities + $totalEquity;

            $totals = [
                'totalAssets' => $totalAssets,
                'totalLiabilities' => $totalLiabilities,
                'totalEquity' => $totalEquity,
                'totalLiabilitiesAndEquity' => $totalLiabilitiesAndEquity,
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'assets' => $assets,
                    'liabilities' => $liabilities,
                    'equity' => $equity,
                    'totals' => $totals,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating balance sheet: ' . $e->getMessage(),
            ], 500);
        }
    }

    private function getCashInHandBalance($fromDate, $toDate)
    {
        // Get closing balance from CashInHandStatementController
        $request = new Request([
            'from_date' => $fromDate->format('Y-m-d'),
            'to_date' => $toDate->format('Y-m-d'),
        ]);

        $response = app(\App\Http\Controllers\CashInHandStatementController::class)->getStatement($request);
        $responseData = json_decode($response->getContent(), true);

        return $responseData['balance'] ?? 0;
    }

    private function getBankAccountBalance($fromDate, $toDate)
    {
        // Get closing balance from BankAccountStatementController
        $request = new Request([
            'from_date' => $fromDate->format('Y-m-d'),
            'to_date' => $toDate->format('Y-m-d'),
        ]);

        $response = app(\App\Http\Controllers\BankAccountStatementController::class)->getStatement($request);
        $responseData = json_decode($response->getContent(), true);

        return $responseData['balance'] ?? 0;
    }

    private function getInventoryValue($fromDate, $toDate)
    {
        // Get total stock cost from StockReportController detailed report
        $request = new Request([
            'from_date' => $fromDate->format('Y-m-d'),
            'to_date' => $toDate->format('Y-m-d'),
        ]);

        $response = app(\App\Http\Controllers\StockReportController::class)->detailedReport($request);
        $responseData = json_decode($response->getContent(), true);

        $totalStockValue = 0;

        if (is_array($responseData)) {
            foreach ($responseData as $item) {
                // Use totalClosingValue which is calculated with cost price (buying cost)
                $totalStockValue += $item['totalClosingValue'] ?? 0;
            }
        }

        return $totalStockValue;
    }



    private function getAccountReceivableBalance($fromDate, $toDate)
    {
        // Get total outstanding amount from outstanding controller logic
        // For balance sheet, we want all outstanding amounts up to the end date
        $response = app(\App\Http\Controllers\OutstandingController::class)->index(new Request());
        $responseData = json_decode($response->getContent(), true);

        // Suppress unused parameter warnings - using controller method that doesn't filter by date
        unset($fromDate, $toDate);
        return $responseData['total_pending_amount'] ?? 0;
    }

    private function getAccountPayableBalance($fromDate, $toDate)
    {
        // Get total payable amount from payable controller logic
        // For balance sheet, we want all payable amounts up to the end date
        $response = app(\App\Http\Controllers\PayableController::class)->index(new Request());
        $responseData = json_decode($response->getContent(), true);

        // Suppress unused parameter warnings - using controller method that doesn't filter by date
        unset($fromDate, $toDate);
        return $responseData['total_pending_amount'] ?? 0;
    }

    private function getLoanBalance($fromDate, $toDate)
    {
        // Get opening balance for Loan Liabilities and its subgroups
        $subGroups = AccountSubGroup::where('main_group', 'Loan Liabilities')->pluck('sub_group_name')->toArray();
        $groups = array_merge(['Loan Liabilities'], $subGroups);
        $openingBalance = StaffLedger::whereIn('account_group', $groups)->sum('opening_balance');

        // Get loan-related transactions from payments
        $loanLedgerIds = StaffLedger::whereIn('account_group', $groups)->pluck('id')->toArray();

        $receiveVouchers = Payment::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $loanLedgerIds)
            ->where('voucher_no', 'like', 'REC-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        $paymentVouchers = Payment::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $loanLedgerIds)
            ->where('voucher_no', 'like', 'PAY-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        // Handle declined cheques for Loan Liabilities
        // Payment voucher cheques declined: add back to balance (since payment was not actually made)
        $declinedPaymentVouchers = ChequeStatement::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $loanLedgerIds)
            ->where('voucher_no', 'like', 'PAY-%')
            ->where('status', 'declined')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        // Receive voucher cheques declined: subtract from balance (since receipt was not actually received)
        $declinedReceiveVouchers = ChequeStatement::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $loanLedgerIds)
            ->where('voucher_no', 'like', 'REC-%')
            ->where('status', 'declined')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        return $openingBalance + $receiveVouchers - $paymentVouchers + $declinedPaymentVouchers - $declinedReceiveVouchers;
    }

    private function getCapitalBalance($fromDate, $toDate)
    {
        // Get opening balance for Capital Account and its subgroups
        $subGroups = AccountSubGroup::where('main_group', 'Capital Account')->pluck('sub_group_name')->toArray();
        $groups = array_merge(['Capital Account'], $subGroups);
        $openingBalance = StaffLedger::whereIn('account_group', $groups)->sum('opening_balance');

        // Get capital-related transactions from payments
        $capitalLedgerIds = StaffLedger::whereIn('account_group', $groups)->pluck('id')->toArray();

        $receiveVouchers = Payment::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $capitalLedgerIds)
            ->where('voucher_no', 'like', 'REC-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        $paymentVouchers = Payment::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $capitalLedgerIds)
            ->where('voucher_no', 'like', 'PAY-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        // Handle declined cheques for Capital Account
        // Payment voucher cheques declined: add back to balance (since payment was not actually made)
        $declinedPaymentVouchers = ChequeStatement::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $capitalLedgerIds)
            ->where('voucher_no', 'like', 'PAY-%')
            ->where('status', 'declined')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        // Receive voucher cheques declined: subtract from balance (since receipt was not actually received)
        $declinedReceiveVouchers = ChequeStatement::where('refer_type', 'Ledger')
            ->whereIn('refer_id', $capitalLedgerIds)
            ->where('voucher_no', 'like', 'REC-%')
            ->where('status', 'declined')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->sum('amount');

        return $openingBalance + $receiveVouchers - $paymentVouchers + $declinedPaymentVouchers - $declinedReceiveVouchers;
    }

    private function getNetProfitBalance($fromDate, $toDate)
    {
        // Get net profit from ProfitAndLossReportController
        $request = new Request([
            'from_date' => $fromDate->format('Y-m-d'),
            'to_date' => $toDate->format('Y-m-d'),
        ]);
        
        $response = app(\App\Http\Controllers\ProfitAndLossReportController::class)->generateProfitAndLoss($request);
        $responseData = json_decode($response->getContent(), true);
        
        return $responseData['totals']['netProfitLoss'] ?? 0;
    }

    private function getDrawingBalance($fromDate, $toDate)
    {
        // Drawing is when owner takes money out - this would be in payment vouchers for owner/drawing accounts
        // For now, we'll return 0 as drawing accounts need to be specifically identified
        // TODO: Implement drawing calculation based on owner withdrawal accounts
        // Suppress unused parameter warnings for future implementation
        unset($fromDate, $toDate);
        return 0;
    }
}
