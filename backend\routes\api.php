<?php
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\SalesReturnNextNumberController;
use App\Http\Controllers\{
    AccountSubGroupController, 
    DashboardController,
    DebugController,
    UnitController,
    ProductController,
    ProductVariantController,
    CategoryController,
    SupplierController,
    StoreLocationController,
    CustomerController,
    SaleController,
    UserController,
    StockReportController,
    AuthController,
    BranchController,
    CompaniesController,
    CompanyController,
    CustomerLoyaltyCardController,
    CustomersLoyaltyCardController,
    DiscountSchemeController,
    LoyaltyCardController,
    LoyaltyCardDesignController,
    RoleController,
    PermissionController,
    ProductionCategoryController,
    ProductionItemController,
    PurchaseController,
    PurchaseOrderController,
    PurchaseReturnController,
    RawMaterialController,
    SalesInvoiceController,
    SalesReturnController,
    RegisterController,
    OutstandingController,
    OutstandingTemplateController,
    PromotionTemplateController,
    QuotationController,
    SalesTemplateController,
    StockRecheckController,
    StockTransferController,
    LedgerStaffController,
    StaffLedgerController,
    PayableController,
    StatementController,
    ChequeStatementController,
    PaymentVoucherController,
    ProjectController,
    ProfitAndLossReportController,
    LoyaltyPointsController
};
use App\Http\Controllers\Api\InvoiceTemplateController;
use App\Http\Middleware\EnsureRegisterIsOpen;
use App\Services\DialogSmsService;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\SubtaskController;


// Authentication routes
Route::middleware(['api'])->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
});


// Global OPTIONS route for CORS
Route::options('{any}', function () {
    return response()->json([], 200);
})->where('any', '.*');

// Auth test route
Route::middleware('auth:api')->get('/test-auth', function () {
    try {
        $user = auth()->user();
        return response()->json([
            'success' => true,
            'user' => $user,
            'token_valid' => true
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 401);
    }
});

// Authenticated routes with role-permission middleware
Route::middleware(['api', 'auth:api', \App\Http\Middleware\RolePermissionMiddleware::class])->group(function () {
    // Auth
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/me', [AuthController::class, 'me']);
    Route::post('/add-default-user', [AuthController::class, 'addDefaultUser']);
    Route::get('/verify-token', [AuthController::class, 'verifyToken']);
    Route::post('/refresh-token', [AuthController::class, 'refreshToken']);

    // Register
    Route::get('/register/status', [RegisterController::class, 'getStatus']);
    Route::post('/register/open', [RegisterController::class, 'openShift']);
    Route::post('/register/close', [RegisterController::class, 'closeShift']);
    Route::post('/register/update-opening-cash', [RegisterController::class, 'updateOpeningCash']);
    Route::post('/register/close-previous-day', [RegisterController::class, 'closePreviousDayRegister']);
    Route::post('/register/cash-in', [RegisterController::class, 'addCash'])->middleware(EnsureRegisterIsOpen::class);
    Route::post('/register/cash-out', [RegisterController::class, 'removeCash'])->middleware(EnsureRegisterIsOpen::class);
    Route::get('/register/current', [RegisterController::class, 'getCurrentRegistry'])->middleware(EnsureRegisterIsOpen::class);
    Route::get('/register/report', [RegisterController::class, 'getStoredRegisterReports'])->middleware(EnsureRegisterIsOpen::class);

    // Roles & Permissions
    Route::apiResource('permissions', PermissionController::class)->except(['update']);
    Route::apiResource('roles', controller: RoleController::class);
    Route::post('/roles/{role}/permissions', [RoleController::class, 'assignPermissions']);

    // Users
    Route::apiResource('users', UserController::class)->except(['create', 'edit']);
    Route::get('users/deleted', [UserController::class, 'getDeletedUsers']);
    Route::post('users/{id}/restore', [UserController::class, 'restoreUser']);
    Route::delete('users/{id}/force', [UserController::class, 'forceDeleteUser']);
    Route::post('users/{user}/permissions', [UserController::class, 'assignPermissions']);
    Route::post('users/change-password', [UserController::class, 'changePassword']);
    Route::put('users/update-profile', [UserController::class, 'updateProfile']);
    Route::get('users/{user}/activity-log', [UserController::class, 'activityLog']);
    Route::post('users/{user}/enable-2fa', [UserController::class, 'enable2FA']);
    Route::post('users/{user}/activate', [UserController::class, 'activateUser']);
    Route::patch('users/{user}/status', [UserController::class, 'updateStatus']);

    // Recycle Bin - Sales
    Route::get('sales/deleted', [SaleController::class, 'getDeletedSales']);
    Route::post('sales/{id}/restore', [SaleController::class, 'restoreSale']);
    Route::delete('sales/{id}/force', [SaleController::class, 'forceDeleteSale']);
    Route::get('sales/{id}/items', [SaleController::class, 'getSaleItems']);

    // Recycle Bin - Invoices
    Route::get('invoices/deleted', [SalesInvoiceController::class, 'getDeletedInvoices']);
    Route::post('invoices/{id}/restore', [SalesInvoiceController::class, 'restoreInvoice']);
    Route::delete('invoices/{id}/force', [SalesInvoiceController::class, 'forceDeleteInvoice']);
    Route::get('invoices/{id}/items', [SalesInvoiceController::class, 'getInvoiceItems']);

    // Recycle Bin - Purchases
    Route::get('purchases/deleted', [PurchaseController::class, 'getDeletedPurchases']);
    Route::post('purchases/{id}/restore', [PurchaseController::class, 'restorePurchase']);
    Route::delete('purchases/{id}/force', [PurchaseController::class, 'forceDeletePurchase']);
    Route::get('purchases/{id}/items', [PurchaseController::class, 'getPurchaseItems']);

    // Recycle Bin - Products
    Route::get('products/deleted', [ProductController::class, 'getDeletedProducts']);
    Route::post('products/{id}/restore', [ProductController::class, 'restoreProduct']);
    Route::delete('products/{id}/force', [ProductController::class, 'forceDeleteProduct']);

    // Recycle Bin - Product Variants
    Route::get('product-variants/deleted', [ProductVariantController::class, 'getDeletedProductVariants']);
    Route::post('product-variants/{id}/restore', [ProductVariantController::class, 'restoreProductVariant']);
    Route::delete('product-variants/{id}/force', [ProductVariantController::class, 'forceDeleteProductVariant']);
   

    // Outstanding
});

// Register
Route::get('/register/status', [RegisterController::class, 'getStatus']);
Route::post('/register/open', [RegisterController::class, 'openShift']);
Route::post('/register/close', [RegisterController::class, 'closeShift']);
Route::post('/register/update-opening-cash', [RegisterController::class, 'updateOpeningCash']);
Route::post('/register/close-previous-day', [RegisterController::class, 'closePreviousDayRegister']);
Route::post('/register/cash-in', [RegisterController::class, 'addCash'])->middleware(EnsureRegisterIsOpen::class);
Route::post('/register/cash-out', [RegisterController::class, 'removeCash'])->middleware(EnsureRegisterIsOpen::class);
Route::get('/register/current', [RegisterController::class, 'getCurrentRegistry'])->middleware(EnsureRegisterIsOpen::class);
Route::get('/register/report', [RegisterController::class, 'getStoredRegisterReports']);

Route::get('/outstanding', [OutstandingController::class, 'index']);
Route::patch('/outstanding/{id}', [OutstandingController::class, 'update']);
Route::get('/payable', [PayableController::class, 'index']);
Route::put('/payable/{id}', [PayableController::class, 'update']);

// Statement Generation
Route::post('/statement/generate', [StatementController::class, 'generateStatement']);
Route::post('/profit-and-loss', [ProfitAndLossReportController::class, 'generateProfitAndLoss']);

// Loyalty Points Report
Route::get('/loyalty-points', [LoyaltyPointsController::class, 'index']);

// Cheque Statement Routes
Route::get('/cheque-statements', [ChequeStatementController::class, 'index']);
Route::get('/cheque-statements/statistics', [ChequeStatementController::class, 'getStatistics']);
Route::patch('/cheque-statements/{id}/complete', [ChequeStatementController::class, 'markAsCompleted']);
Route::patch('/cheque-statements/{id}/decline', [ChequeStatementController::class, 'markAsDeclined']);

// Voucher number generation routes
Route::get('/voucher/next-payment-number', [StaffLedgerController::class, 'getNextPaymentVoucherNumber']);
Route::get('/voucher/next-receive-number', [StaffLedgerController::class, 'getNextReceiveVoucherNumber']);

Route::prefix('staff-ledger')->group(function () {
    Route::get('/', [StaffLedgerController::class, 'index']);
    Route::post('/', [StaffLedgerController::class, 'store']);
    Route::get('/vouchers', [StaffLedgerController::class, 'getLedgerForVouchers']);
    Route::post('/payment-voucher', [StaffLedgerController::class, 'storePaymentVoucher']);
    Route::post('/receive-voucher', [StaffLedgerController::class, 'storeReceiveVoucher']);
    Route::get('/{id}', [StaffLedgerController::class, 'show']);
    Route::post('/{id}', [StaffLedgerController::class, 'update']);
    Route::delete('/{id}', [StaffLedgerController::class, 'destroy']);
});

// Account Sub Groups
Route::prefix('account-sub-groups')->group(function () {
    Route::get('/', [AccountSubGroupController::class, 'index']);
    Route::post('/', [AccountSubGroupController::class, 'store']);
    Route::get('/{id}', [AccountSubGroupController::class, 'show']);
    Route::put('/{id}', [AccountSubGroupController::class, 'update']);
    Route::delete('/{id}', [AccountSubGroupController::class, 'destroy']);
    Route::post('/bulk-delete', [AccountSubGroupController::class, 'bulkDelete']);
});

// Discount Schemes
Route::prefix('discount-schemes')->group(function () {
    Route::get('/', [DiscountSchemeController::class, 'index']);
    Route::post('/', [DiscountSchemeController::class, 'store']);
    Route::get('/{scheme}', [DiscountSchemeController::class, 'show']);
    Route::put('/{scheme}', [DiscountSchemeController::class, 'update']);
    Route::delete('/{scheme}', [DiscountSchemeController::class, 'destroy']);
});

// Master data
Route::get('/products/check-names', [ProductController::class, 'checkNames']);
Route::apiResource('products', ProductController::class);
Route::post('/products/import', [ProductController::class, 'import']);
Route::post('/products/{id}/addBatch', [ProductController::class, 'addBatch']);
Route::get('/product/{id}', [ProductController::class, 'barcode']);
Route::apiResource('categories', CategoryController::class);
Route::apiResource('store-locations', StoreLocationController::class);
// Supplier Recycle Bin
Route::get('suppliers/deleted', [SupplierController::class, 'deleted']);
Route::post('suppliers/{id}/restore', [SupplierController::class, 'restore']);
Route::delete('suppliers/{id}/force', [SupplierController::class, 'forceDelete']);
Route::apiResource('suppliers', SupplierController::class);
Route::apiResource('units', UnitController::class);
Route::get('/customers/deleted', [CustomerController::class, 'deleted']);
Route::post('/customers/{id}/restore', [CustomerController::class, 'restore']);
Route::delete('/customers/{id}/force', [CustomerController::class, 'forceDelete']);
Route::apiResource('customers', CustomerController::class);
Route::apiResource('production-categories', ProductionCategoryController::class);
Route::apiResource('production-items', ProductionItemController::class);
Route::apiResource('raw-materials', RawMaterialController::class);
Route::get('suppliers', [RawMaterialController::class, 'getSuppliers']);
Route::get('units', [RawMaterialController::class, 'getUnits']);
// Product Variants
Route::apiResource('product-variants', ProductVariantController::class);
Route::get('/product-variants/search-by-barcode', [ProductVariantController::class, 'searchByBarcode']);

// Sales
Route::get('/next-bill-number', [SaleController::class, 'getLastBillNumber']);
Route::get('/sales/daily-profit-report', [SaleController::class, 'getcombinedDailyProfitReport']);
Route::get('/sales/bill-wise-profit-report', [SaleController::class, 'getCombinedBillWiseProfitReport']);
Route::get('/sales/company-wise-profit-report', [SaleController::class, 'getCompanyWiseProfitReport']);
Route::get('/sales/supplier-wise-profit-report', [SaleController::class, 'getSupplierWiseProfitReport']);
Route::get('/sales/category-wise-profit-report', [SaleController::class, 'getCategoryWiseProfitReport']);
Route::get('/sales/store-location-wise-profit-report', [SaleController::class, 'getStoreLocationWiseProfitReport']);
Route::get('/sales/customer-wise-profit-report', [SaleController::class, 'getCustomerWiseProfitReport']);
Route::apiResource('sales', SaleController::class);
// Route::post('/sales', [SaleController::class, 'store'])->middleware('register.check');
// Stock reports
Route::get('/stock-reports', [StockReportController::class, 'index']);
Route::get('/detailed-stock-reports', [StockReportController::class, 'detailedReport']);
Route::post('/update-batch-closing-stock', [StockReportController::class, 'updateBatchClosingStock']);
Route::get('/batch-stock-reports', [StockReportController::class, 'batchReport']);

// Held Sales
Route::prefix('holds')->group(function () {
    Route::get('/', [\App\Http\Controllers\Api\HeldSaleController::class, 'index']);
    Route::post('/', [\App\Http\Controllers\Api\HeldSaleController::class, 'store']);
    Route::get('/{id}', [\App\Http\Controllers\Api\HeldSaleController::class, 'show']);
    Route::delete('/{id}', [\App\Http\Controllers\Api\HeldSaleController::class, 'destroy']);
    Route::post('/{id}/recall', [\App\Http\Controllers\Api\HeldSaleController::class, 'recall']);
});

// Purchases
Route::apiResource('purchases', PurchaseController::class);

// Purchase Returns
Route::post('/purchase-returns', [PurchaseReturnController::class, 'createPurchaseReturn']);
Route::get('/purchase-returns', [PurchaseReturnController::class, 'getPurchaseReturns']);
Route::get('/purchase-returns/{id}', [PurchaseReturnController::class, 'show']);
Route::put('/purchase-returns/{id}', [PurchaseReturnController::class, 'update']);
Route::delete('/purchase-returns/{id}', [PurchaseReturnController::class, 'destroy']);

// Purchase Orders
Route::apiResource('purchase-orders', PurchaseOrderController::class);

// Companies
Route::prefix('companies')->group(function () {
    Route::get('/', [CompanyController::class, 'index']);
    Route::post('/', [CompanyController::class, 'store']);
    Route::get('/{company_name}', [CompanyController::class, 'show']);
    Route::put('/{company_name}', [CompanyController::class, 'update']);
    Route::delete('/{company_name}', [CompanyController::class, 'destroy']);
});

// Sales Invoice
Route::apiResource('invoices', SalesInvoiceController::class);
Route::post('/invoices/{id}/approve', [SalesInvoiceController::class, 'approve']);
Route::post('/invoices/{id}/reject', [SalesInvoiceController::class, 'reject']);
Route::get('/invoices/check-invoice-no', [SalesInvoiceController::class, 'checkInvoiceNo']);

// Sales Returns
Route::get('/sales-returns/next-number', [SalesReturnNextNumberController::class, 'getNextNumber']);
Route::resource('sales-returns', SalesReturnController::class)->except(['create', 'edit']);

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'dashboard']);

// Route::get('/customers', [CustomersLoyaltyCardController::class, 'index']);
// Route::post('/customers', [CustomersLoyaltyCardController::class, 'store']);

Route::get('/loyalty-cards', [LoyaltyCardController::class, 'index']);
Route::post('/loyalty-cards', [LoyaltyCardController::class, 'store']);
Route::get('/loyalty-cards/{id}', [LoyaltyCardController::class, 'show']);
Route::put('/loyalty-cards/{id}', [LoyaltyCardController::class, 'update']);
Route::patch('/loyalty-cards/{id}', [LoyaltyCardController::class, 'update']);
Route::delete('/loyalty-cards/{id}', [LoyaltyCardController::class, 'destroy']);

Route::apiResource('loyalty-card-designs', LoyaltyCardDesignController::class);

Route::get('/quotations', [QuotationController::class, 'index']);
Route::post('/quotations', [QuotationController::class, 'store']);
Route::get('/quotations/{id}', [QuotationController::class, 'show']);
Route::put('/quotations/{id}', [QuotationController::class, 'update']);
Route::delete('/quotations/{id}', [QuotationController::class, 'destroy']);




Route::apiResource('promotion-templates', PromotionTemplateController::class)->only(['index', 'show', 'store']);

Route::apiResource('sales-templates', SalesTemplateController::class)->only(['index', 'store', 'update', 'destroy']);

Route::post('sales-templates/{id}/set-default', [SalesTemplateController::class, 'setDefault']);
Route::post('send-message', [SalesTemplateController::class, 'sendMessage']);

Route::prefix('templates')->group(function () {
    Route::get('/', [OutstandingTemplateController::class, 'index']);
    Route::get('/{id}', [OutstandingTemplateController::class, 'show']);
    Route::post('/', [OutstandingTemplateController::class, 'store']);
    Route::put('/{id}', [OutstandingTemplateController::class, 'update']);
    Route::delete('/{id}', [OutstandingTemplateController::class, 'destroy']);
});



Route::post('/send-dialog-sms', function (Request $request) {
    $data = $request->validate([
        'messages' => 'required|array',
        'messages.*.number' => 'required|string',
        'messages.*.text' => 'required|string',
        'messages.*.clientRef' => 'sometimes|string',
        'messages.*.mask' => 'sometimes|string',
        'messages.*.campaignName' => 'sometimes|string',
    ]);

    $dialogSmsService = new DialogSmsService();
    $result = $dialogSmsService->sendBatch($data['messages']);

    if (isset($result['error'])) {
        return response()->json([
            'success' => false,
            'error' => $result['error'],
            'resultCode' => -1
        ], 500);
    }

    return response()->json([
        'success' => true,
        'resultCode' => 0,
        'data' => $result
    ]);
});


// Authentication routes
Route::middleware(['api'])->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
});

Route::post('/projects', [ProjectController::class, 'store']);
Route::put('/projects/{id}', [ProjectController::class, 'update']);
Route::get('/projects', [ProjectController::class, 'index']);

// Product barcode check route
Route::get('/products/check-barcode', [ProductController::class, 'checkBarcode']);

// Product item code check route
Route::get('/products/check-item-code', [ProductController::class, 'checkItemCode']);

// Task routes
Route::get('/tasks', [TaskController::class, 'index']);
Route::post('/tasks', [TaskController::class, 'store']);
Route::get('/tasks/{id}', [TaskController::class, 'show']);
Route::put('/tasks/{id}', [TaskController::class, 'update']);
Route::delete('/tasks/{id}', [TaskController::class, 'destroy']);
Route::get('/tasks/project/{projectId}', [TaskController::class, 'getByProject']);
Route::get('/tasks/user/{userId}', [TaskController::class, 'getAssignedToUser']);
Route::get('/task-users', [TaskController::class, 'getUsers']);

// Global OPTIONS route for CORS
Route::options('{any}', function () {
    return response()->json([], 200);
})->where('any', '.*');

// Auth test route
Route::middleware('auth:api')->get('/test-auth', function () {
    try {
        $user = auth()->user();
        return response()->json([
            'success' => true,
            'user' => $user,
            'token_valid' => true
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 401);
    }
});

// Authenticated routes with role-permission middleware
Route::middleware(['api', 'auth:api', \App\Http\Middleware\RolePermissionMiddleware::class])->group(function () {
    // Auth
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/me', [AuthController::class, 'me']);
    Route::post('/add-default-user', [AuthController::class, 'addDefaultUser']);
    Route::get('/verify-token', [AuthController::class, 'verifyToken']);
    Route::post('/refresh-token', [AuthController::class, 'refreshToken']);

    // Register
    Route::get('/register/status', [RegisterController::class, 'getStatus']);
    Route::post('/register/open', [RegisterController::class, 'openShift']);
    Route::post('/register/close', [RegisterController::class, 'closeShift']);
    Route::post('/register/update-opening-cash', [RegisterController::class, 'updateOpeningCash']);
    Route::post('/register/close-previous-day', [RegisterController::class, 'closePreviousDayRegister']);
    Route::post('/register/cash-in', [RegisterController::class, 'addCash'])->middleware(EnsureRegisterIsOpen::class);
    Route::post('/register/cash-out', [RegisterController::class, 'removeCash'])->middleware(EnsureRegisterIsOpen::class);
    Route::get('/register/current', [RegisterController::class, 'getCurrentRegistry'])->middleware(EnsureRegisterIsOpen::class);
    Route::get('/register/report', [RegisterController::class, 'getStoredRegisterReports'])->middleware(EnsureRegisterIsOpen::class);

    // Roles & Permissions
    Route::apiResource('permissions', PermissionController::class)->except(['update']);
    Route::apiResource('roles', controller: RoleController::class);
    Route::post('/roles/{role}/permissions', [RoleController::class, 'assignPermissions']);

    // Users
    Route::apiResource('users', UserController::class)->except(['create', 'edit']);
    Route::get('users/deleted', [UserController::class, 'getDeletedUsers']);
    Route::post('users/{id}/restore', [UserController::class, 'restoreUser']);
    Route::delete('users/{id}/force', [UserController::class, 'forceDeleteUser']);
    Route::post('users/{user}/permissions', [UserController::class, 'assignPermissions']);
    Route::post('users/change-password', [UserController::class, 'changePassword']);
    Route::put('users/update-profile', [UserController::class, 'updateProfile']);
    Route::get('users/{user}/activity-log', [UserController::class, 'activityLog']);
    Route::post('users/{user}/enable-2fa', [UserController::class, 'enable2FA']);
    Route::post('users/{user}/activate', [UserController::class, 'activateUser']);
    Route::patch('users/{user}/status', [UserController::class, 'updateStatus']);

    // Outstanding
});

// Register
Route::get('/register/status', [RegisterController::class, 'getStatus']);
Route::post('/register/open', [RegisterController::class, 'openShift']);
Route::post('/register/close', [RegisterController::class, 'closeShift']);
Route::post('/register/update-opening-cash', [RegisterController::class, 'updateOpeningCash']);
Route::post('/register/close-previous-day', [RegisterController::class, 'closePreviousDayRegister']);
Route::post('/register/cash-in', [RegisterController::class, 'addCash'])->middleware(EnsureRegisterIsOpen::class);
Route::post('/register/cash-out', [RegisterController::class, 'removeCash'])->middleware(EnsureRegisterIsOpen::class);
Route::get('/register/current', [RegisterController::class, 'getCurrentRegistry'])->middleware(EnsureRegisterIsOpen::class);
Route::get('/register/report', [RegisterController::class, 'getStoredRegisterReports']);

Route::get('/outstanding', [OutstandingController::class, 'index']);
Route::patch('/outstanding/{id}', [OutstandingController::class, 'update']);

// Invoice Template Routes
Route::get('/invoice-templates', [InvoiceTemplateController::class, 'index']);
Route::post('/invoice-templates', [InvoiceTemplateController::class, 'store']);
Route::get('/invoice-templates/{id}', [InvoiceTemplateController::class, 'show']);
Route::put('/invoice-templates/{id}', [InvoiceTemplateController::class, 'update']);
Route::delete('/invoice-templates/{id}', [InvoiceTemplateController::class, 'destroy']);
Route::post('/invoice-templates/{id}/set-default', [InvoiceTemplateController::class, 'setDefault']);
Route::post('/invoice-templates/{id}/remove-default', [InvoiceTemplateController::class, 'removeDefault']);
Route::get('/invoice-templates/default', [InvoiceTemplateController::class, 'getDefault']);
Route::get('/invoice-templates/default/test', [InvoiceTemplateController::class, 'debugList']);


Route::get('/companies', [CompaniesController::class, 'index']);
Route::post('/companies', [CompaniesController::class, 'store']);
Route::put('/companies/{id}', [CompaniesController::class, 'update']);
Route::delete('/companies/{id}', [CompaniesController::class, 'destroy']);

// Discount Schemes
Route::prefix('discount-schemes')->group(function () {
    Route::get('/', [DiscountSchemeController::class, 'index']);
    Route::post('/', [DiscountSchemeController::class, 'store']);
    Route::get('/{scheme}', [DiscountSchemeController::class, 'show']);
    Route::put('/{scheme}', [DiscountSchemeController::class, 'update']);
    Route::delete('/{scheme}', [DiscountSchemeController::class, 'destroy']);
});

// Master data
Route::apiResource('products', ProductController::class);
Route::post('/products/import', [ProductController::class, 'import']);
Route::get('/products/check-names', [ProductController::class, 'checkNames']);
Route::post('/products/{product_id}/addBatch', [ProductController::class, 'addBatch']);
Route::get('/product/{id}', [ProductController::class, 'barcode']);
Route::get('/products/check-item-code', [ProductController::class, 'checkItemCode']);
Route::get('/products/check-barcode', [ProductController::class, 'checkBarcode']);

// Product Variants
Route::apiResource('product-variants', ProductVariantController::class);


Route::apiResource('categories', CategoryController::class);
Route::apiResource('store-locations', StoreLocationController::class);
Route::apiResource('suppliers', SupplierController::class);
Route::apiResource('units', UnitController::class);
Route::get('/customers/deleted', [CustomerController::class, 'deleted']);
Route::apiResource('customers', CustomerController::class);
Route::apiResource('production-categories', ProductionCategoryController::class);
Route::apiResource('production-items', ProductionItemController::class);
Route::apiResource('raw-materials', RawMaterialController::class);
Route::get('suppliers', [RawMaterialController::class, 'getSuppliers']);
Route::get('units', [RawMaterialController::class, 'getUnits']);

// Sales
Route::get('/next-bill-number', [SaleController::class, 'getLastBillNumber']);
Route::get('/sales/daily-profit-report', [SaleController::class, 'getcombinedDailyProfitReport']);
Route::get('/sales/bill-wise-profit-report', [SaleController::class, 'getCombinedBillWiseProfitReport']);
Route::get('/sales/company-wise-profit-report', [SaleController::class, 'getCompanyWiseProfitReport']);
Route::get('/sales/supplier-wise-profit-report', [SaleController::class, 'getSupplierWiseProfitReport']);
Route::get('/sales/category-wise-profit-report', [SaleController::class, 'getCategoryWiseProfitReport']);
Route::get('/sales/store-location-wise-profit-report', [SaleController::class, 'getStoreLocationWiseProfitReport']);
Route::get('/sales/customer-wise-profit-report', [SaleController::class, 'getCustomerWiseProfitReport']);
Route::apiResource('sales', SaleController::class);
// Route::post('/sales', [SaleController::class, 'store'])->middleware('register.check');
// Stock reports
Route::get('/stock-reports', [StockReportController::class, 'index']);
Route::get('/detailed-stock-reports', [StockReportController::class, 'detailedReport']);
Route::post('/update-batch-closing-stock', [StockReportController::class, 'updateBatchClosingStock']);
Route::get('/batch-stock-reports', [StockReportController::class, 'batchReport']);

// Held Sales
Route::prefix('holds')->group(function () {
    Route::get('/', [\App\Http\Controllers\Api\HeldSaleController::class, 'index']);
    Route::post('/', [\App\Http\Controllers\Api\HeldSaleController::class, 'store']);
    Route::get('/{id}', [\App\Http\Controllers\Api\HeldSaleController::class, 'show']);
    Route::delete('/{id}', [\App\Http\Controllers\Api\HeldSaleController::class, 'destroy']);
    Route::post('/{id}/recall', [\App\Http\Controllers\Api\HeldSaleController::class, 'recall']);
});

// Purchases
Route::apiResource('purchases', PurchaseController::class);

// Purchase Returns
Route::post('/purchase-returns', [PurchaseReturnController::class, 'createPurchaseReturn']);
Route::get('/purchase-returns', [PurchaseReturnController::class, 'getPurchaseReturns']);
Route::get('/purchase-returns/{id}', [PurchaseReturnController::class, 'show']);
Route::put('/purchase-returns/{id}', [PurchaseReturnController::class, 'update']);
Route::delete('/purchase-returns/{id}', [PurchaseReturnController::class, 'destroy']);

// Purchase Orders
Route::apiResource('purchase-orders', PurchaseOrderController::class);

// Companies
Route::prefix('companies')->group(function () {
    Route::get('/', [CompanyController::class, 'index']);
    Route::post('/', [CompanyController::class, 'store']);
    Route::get('/{company_name}', [CompanyController::class, 'show']);
    Route::put('/{company_name}', [CompanyController::class, 'update']);
    Route::delete('/{company_name}', [CompanyController::class, 'destroy']);
});

// Sales Invoice
Route::apiResource('invoices', SalesInvoiceController::class);
Route::get('/invoices/check-invoice-no', [SalesInvoiceController::class, 'checkInvoiceNo']);

// Sales Returns
Route::get('/sales-returns/next-number', [SalesReturnNextNumberController::class, 'getNextNumber']);
Route::resource('sales-returns', SalesReturnController::class)->except(['create', 'edit']);

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'dashboard']);

// Route::get('/customers', [CustomersLoyaltyCardController::class, 'index']);
// Route::post('/customers', [CustomersLoyaltyCardController::class, 'store']);

Route::get('/loyalty-cards', [LoyaltyCardController::class, 'index']);
Route::post('/loyalty-cards', [LoyaltyCardController::class, 'store']);
Route::get('/loyalty-cards/{id}', [LoyaltyCardController::class, 'show']);
Route::put('/loyalty-cards/{id}', [LoyaltyCardController::class, 'update']);
Route::patch('/loyalty-cards/{id}', [LoyaltyCardController::class, 'update']);
Route::delete('/loyalty-cards/{id}', [LoyaltyCardController::class, 'destroy']);

Route::apiResource('loyalty-card-designs', LoyaltyCardDesignController::class);

Route::get('/quotations', [QuotationController::class, 'index']);
Route::post('/quotations', [QuotationController::class, 'store']);
Route::get('/quotations/{id}', [QuotationController::class, 'show']);
Route::put('/quotations/{id}', [QuotationController::class, 'update']);
Route::delete('/quotations/{id}', [QuotationController::class, 'destroy']);
Route::get('/quotation-users', [QuotationController::class, 'getUsers']);
Route::post('/quotations/{id}/approve', [QuotationController::class, 'approve']);
Route::post('/quotations/{id}/reject', [QuotationController::class, 'reject']);
Route::get('/quotation-users', [QuotationController::class, 'getUsers']);

Route::get('/companies', [CompaniesController::class, 'index']);
Route::post('/companies', [CompaniesController::class, 'store']);
Route::put('/companies/{id}', [CompaniesController::class, 'update']);
Route::delete('/companies/{id}', [CompaniesController::class, 'destroy']);

Route::apiResource('promotion-templates', PromotionTemplateController::class)->only(['index', 'show', 'store']);

Route::apiResource('sales-templates', SalesTemplateController::class)->only(['index', 'store', 'update', 'destroy']);

Route::post('sales-templates/{id}/set-default', [SalesTemplateController::class, 'setDefault']);
Route::post('send-message', [SalesTemplateController::class, 'sendMessage']);

Route::prefix('templates')->group(function () {
    Route::get('/', [OutstandingTemplateController::class, 'index']);
    Route::get('/{id}', [OutstandingTemplateController::class, 'show']);
    Route::post('/', [OutstandingTemplateController::class, 'store']);
    Route::put('/{id}', [OutstandingTemplateController::class, 'update']);
    Route::delete('/{id}', [OutstandingTemplateController::class, 'destroy']);
});

Route::apiResource('branches', BranchController::class)->parameters([
        'branches' => 'branch_id',
    ]);

// Branch Users Management Routes
Route::get('/branches/{branch}/users', [BranchController::class, 'getBranchUsers']);
Route::post('/branches/{branch}/users', [BranchController::class, 'addUserToBranch']);
Route::put('/branches/{branch}/users/{user}', [BranchController::class, 'updateBranchUser']);
Route::delete('/branches/{branch}/users/{user}', [BranchController::class, 'removeUserFromBranch']);
Route::patch('/branches/{branch}/users/{user}/toggle', [BranchController::class, 'toggleUserStatus']);
Route::delete('/branches/{branch}/users/{user}/permanent', [BranchController::class, 'permanentlyRemoveUserFromBranch']);
Route::get('/branches/{branch}/available-users', [BranchController::class, 'getAvailableUsers']);
Route::get('/stock-rechecks', [StockRecheckController::class, 'index']);
Route::post('/stock-rechecks', [StockRecheckController::class, 'store']);
Route::patch('/stock-rechecks/{id}', [StockRecheckController::class, 'update']);
Route::delete('/stock-rechecks/{id}', [StockRecheckController::class, 'destroy']);
Route::get('/locations', [StockRecheckController::class, 'locations']);
Route::get('/stock-rechecks/batches/{itemCode}', [StockRecheckController::class, 'getProductBatches']);
Route::get('/stock-rechecks/current-system-qty', [StockRecheckController::class, 'getCurrentSystemQuantity']);

Route::get('/stock-transfers', [StockTransferController::class, 'index']);
Route::get('/stock-transfers/pending/{branchId}', [StockTransferController::class, 'pendingForBranch']);
Route::post('/stock-transfers', [StockTransferController::class, 'store']);
Route::put('/stock-transfers/{id}', [StockTransferController::class, 'update']);
Route::patch('/stock-transfers/{id}/receive', [StockTransferController::class, 'receiveTransfer']);
Route::delete('/stock-transfers/{id}', [StockTransferController::class, 'destroy']);

//SMS api
Route::post('/send-dialog-sms', function (Request $request) {
    $data = $request->validate([
        'messages' => 'required|array',
        'messages.*.number' => 'required|string',
        'messages.*.text' => 'required|string',
        'messages.*.clientRef' => 'sometimes|string',
        'messages.*.mask' => 'sometimes|string',
        'messages.*.campaignName' => 'sometimes|string',
    ]);

    $dialogSmsService = new DialogSmsService();
    $result = $dialogSmsService->sendBatch($data['messages']);

    if (isset($result['error'])) {
        return response()->json([
            'success' => false,
            'error' => $result['error'],
            'resultCode' => -1
        ], 500);
    }

    return response()->json([
        'success' => true,
        'resultCode' => 0,
        'data' => $result
    ]);
});

// Subtasks API
Route::apiResource('subtasks', SubtaskController::class);

// Receive Voucher Transaction routes
Route::get('/receive-vouchers', [\App\Http\Controllers\ReceiveVoucherController::class, 'index']);
// Add this route for deleted receive vouchers (must come before {id} routes)
Route::get('/receive-vouchers/deleted', [\App\Http\Controllers\ReceiveVoucherController::class, 'deleted']);
Route::post('/receive-vouchers/{id}/restore', [\App\Http\Controllers\ReceiveVoucherController::class, 'restore']);
Route::delete('/receive-vouchers/{id}/force', [\App\Http\Controllers\ReceiveVoucherController::class, 'forceDelete']);
Route::get('/receive-vouchers/{id}', [\App\Http\Controllers\ReceiveVoucherController::class, 'show']);
Route::put('/receive-vouchers/{id}', [\App\Http\Controllers\ReceiveVoucherController::class, 'update']);
Route::delete('/receive-vouchers/{id}', [\App\Http\Controllers\ReceiveVoucherController::class, 'destroy']);

Route::prefix('payment-vouchers')->group(function () {
    Route::get('/', [PaymentVoucherController::class, 'index']);
    // Add this route for deleted payment vouchers (must come before {id} routes)
    Route::get('/deleted', [PaymentVoucherController::class, 'deleted']);
    Route::post('/{id}/restore', [PaymentVoucherController::class, 'restore']);
    Route::delete('/{id}/force', [PaymentVoucherController::class, 'forceDelete']);
    Route::get('/{id}', [PaymentVoucherController::class, 'show']);
    Route::delete('/{id}', [PaymentVoucherController::class, 'destroy']);
});

// Debug routes - remove in production
Route::get('/debug/users/{userId}/branch-check', [DebugController::class, 'checkUserBranchAssignment']);
Route::get('/users/{userId}/branch', [UserController::class, 'getUserBranch']);

Route::post('/cash-in-hand-statement', [\App\Http\Controllers\CashInHandStatementController::class, 'getStatement']);

// Bank Account Statement
Route::post('/bank-account-statement', [\App\Http\Controllers\BankAccountStatementController::class, 'getStatement']);
