import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { FiX, FiCheck, FiSave, FiUserPlus, FiUser, FiLock, FiInfo, FiShield, FiShoppingCart, FiPackage, FiDollarSign, FiSettings, FiUsers, FiFileText, FiClipboard, FiTruck, FiHome, FiGrid, FiTag, FiPrinter, FiCalendar, FiPieChart, FiLayers, FiDatabase, FiTool, FiBarChart2, FiChevronDown ,FiBookOpen} from 'react-icons/fi';

const PAGE_CATEGORIES = [
    {
        name: 'Dashboard',
        icon: <FiHome />,
        pages: [
            { path: 'dashboard', name: 'Dashboard' }
        ]
    },
    {
        name: 'Inventory Management',
        icon: <FiPackage />,
        pages: [
            { path: 'items', name: 'Items Management' },
            { path: 'expiry', name: 'Expiry Management' },
            { path: 'store-locations', name: 'Store Locations' },
            { path: 'BarcodePage', name: 'Barcode Management' },
            { path: 'units', name: 'Units' },
            { path: 'categories', name: 'Categories' },
            { path: 'companies', name: 'Companies' },
            { path: 'suppliers', name: 'Suppliers' },
            // { path: 'StockTransfer', name: 'Stock Transfer' }, // Not used
            // { path: 'CategoryForm', name: 'Category Form' },
            // { path: 'UnitForm', name: 'Unit Form' }
        ]
    },
    {
        name: 'Sales',
        icon: <FiDollarSign />,
        pages: [
            { path: 'sales', name: 'Sales' },
            { path: 'SalesReturn', name: 'Sales Return' },
            { path: 'Customers', name: 'Customer Management' },
            { path: 'tax', name: 'Tax' },
            // { path: 'SalesInvoice', name: 'Sales Invoice' },
            // { path: 'quotation', name: 'Quotation' },
        ]
    },
    {
        name: 'Purchasing',
        icon: <FiShoppingCart />,
        pages: [
            { path: 'purchasing', name: 'Purchasing' },
            { path: 'PurchaseOrder', name: 'Purchase Order' },
            { path: 'PurchaseReturn', name: 'Purchase Return' },
            // { path: 'SupplierForm', name: 'Supplier Form' }
            // { path: 'PurchaseInvoice', name: 'Purchase Invoice' },
        ]
    },
    {
        name: 'Financial Accounting',
        icon: <FiDollarSign />,
        pages: [
            { path: 'outstanding', name: 'Outstanding' },
            { path: 'bill-by-bill-collection', name: 'Bill by Bill Collection' },
            { path: 'aging-analysis', name: 'Aging Analysis' },
            { path: 'payable', name: 'Payable' },
            { path: 'profit-and-loss-report', name: 'Profit and Loss Report' },
            { path: 'cash-in-hand', name: 'Cash in hand Statement' },
            { path: 'bank-account', name: 'Bank Account Statement' },
            { path: 'balance-sheet', name: 'Balance Sheet' },
            { path: 'trial-balance', name: 'Trial Balance' } 
        ]
    },
    {
        name: 'Voucher',
        icon: <FiFileText />,
        pages: [
            // { path: 'voucher', name: 'Voucher' },
            { path: 'voucher/ReceiveVoucher', name: 'Receive Voucher' },
            { path: 'voucher/PaymentVoucher', name: 'Payment Voucher' }
        ]
    },
    {
        name: 'Ledger',
        icon: <FiBookOpen />,
        pages: [
            // { path: 'ledger', name: 'Ledger' },
            { path: 'ledger/new-ledger', name: 'New Ledger' },
            { path: 'ledger/statement', name: 'Statement' }
        ]
    },
    {
        name: 'Profit',
        icon: <FiDollarSign />,
        pages: [
            
            { path: 'ItemWiseProfit', name: 'Item Wise Profit' },
            { path: 'BillWiseProfit', name: 'Bill Wise Profit' },
            { path: 'CustomerWiseProfit', name: 'Customer Wise Profit' },
            { path: 'SupplierWiseProfit', name: 'Supplier Wise Profit' },
            { path: 'StoreAndLocationWiseProfit', name: 'Store and Location Wise Profit' },
            { path: 'CategoryWiseProfit', name: 'Category Wise Profit' },
            { path: 'CompanyWiseProfit', name: 'Company Wise Profit' },
        ]
    },
    {
        name: 'Reports & Analytics',
        icon: <FiBarChart2 />,
        pages: [
            { path: 'StockReport', name: 'Stock Report' },
            { path: 'ItemWiseStockReport', name: 'Item Wise Report' },
            { path: 'StockRecheck', name: 'Stock Recheck' },
            { path: 'Monthly-wise-Report', name: 'Monthly wise Report' },
            { path: 'reports/registry', name: 'Registry Reports' },
            { path: 'reports/dailysales', name: 'Dailysales Reports' },
            // { path: 'reports', name: 'Reports Dashboard' },
            // { path: 'ReportTable', name: 'Report Table' },
        ]
    },
    {
        name: 'Production',
        icon: <FiTool />,
        pages: [
            { path: 'production', name: 'Production' },
            // { path: 'MakeProductForm', name: 'Make Product' },
            // { path: 'ProductModal', name: 'Product Management' },
            // { path: 'ProductionCategoryModal', name: 'Production Categories' },
            // { path: 'RawMaterialModal', name: 'Raw Materials' }
        ]
    },
    {
        name: 'Approvels',
        icon: <FiCheck />,
        pages: [
            { path: 'Approvels', name: 'Approvels' }
        ]
    },
    {
        name: 'Discount',
        icon: <FiTag />,
        pages: [
            { path: 'DiscountScheam', name: 'Discount Scheme' }
        ]
    },
    {
        name: 'SMS',
        icon: <FiClipboard />,
        pages: [
            { path: 'Sms-template', name: 'SMS Template' }
        ]
    },
    {
        name: 'Task Management',
        icon: <FiClipboard />,
        pages: [
            { path: 'HomePage', name: 'Task Manager' },
            { path: 'TasksPage', name: 'Tasks' },
            { path: 'ProjectsPage', name: 'Projects' },
            { path: 'ReportPage', name: 'Task Reports' },
            { path: 'SubtasksPage', name: 'Subtasks' },
            // { path: 'TaskManager', name: 'Task Management' },
            // { path: 'ProjectForm', name: 'Project Form' },
            // { path: 'SubtaskForm', name: 'Subtask Form' },
            // { path: 'TaskForm', name: 'Task Form' }
        ]
    },
    {
        name: 'Staff Management',
        icon: <FiUsers />,
        pages: [
            { path: 'StaffManagement', name: 'Staff' },
            // { path: 'StaffRegistration', name: 'Staff Registration' },
            // { path: 'RoleBasedAccessControl', name: 'Access Control' },
            // { path: 'AttendanceShiftManagement', name: 'Attendance & Shifts' },
            // { path: 'PayrollSalaryManagement', name: 'Payroll & Salary' },
        ]
    },
    {
        name: 'Loyalty',
        icon: <FiTag />,
        pages: [
            // { path: 'loyalty', name: 'Loyalty' },
            { path: 'loyalty/report', name: 'Loyalty Report' },
            { path: 'loyalty/generate-card', name: 'Loyalty Card Generate' },
            { path: 'loyalty/design-card', name: 'Loyalty Card Design' }
        ]
    },
    {
        name: 'User Management',
        icon: <FiUsers />,
        pages: [
            { path: 'RoleList', name: 'Role Management' },
            { path: 'UserList', name: 'Users' },
            { path: 'RecycleBin', name: 'Recycle Bin' },
            // { path: 'UserModal', name: 'User Management' },
            // { path: 'PERMISSIONS', name: 'Permissions' }, // Not used
        ]
    },
    {
        name: 'Branch Management',
        icon: <FiSettings />,
        pages: [
            { path: 'branch-management/create-branch', name: 'Create Branch' },
            { path: 'branch-management/sales-report', name: 'Sales Report' },
            { path: 'branch-management/stock-report', name: 'Stock Report' },
            { path: 'branch-management/stock-transfer', name: 'Stock Transfer' }, // Not used
            { path: 'branch-management/receive-stock', name: 'Receive Stock' },
            // { path: 'branch-management', name: 'Branch Management' },
        ]
    },
    {
        name: 'Administration',
        icon: <FiSettings />,
        pages: [
            { path: 'CreateCompany', name: 'Company Setup' },
            { path: 'InvoiceTemplate', name: 'Invoice Template' }
        ]
    },
    {
        name: 'POS',
        icon: <FiPrinter />,
        pages: [
            { path: 'pos', name: 'POS' },
            { path: 'touchpos', name: 'Touch POS' },
            // { path: 'billPrintModel', name: 'Bill Printing' }
        ]
    },
    
];

const RoleModal = ({
    isOpen = false,
    onClose = () => { },
    onSubmit = () => { },
    roleData = null
}) => {
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [permissions, setPermissions] = useState([]);
    const permissionsRef = useRef([]);
    const [activeTab, setActiveTab] = useState('details');
    const [expandedCategories, setExpandedCategories] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        if (roleData) {
            setName(roleData.name || '');
            setDescription(roleData.description || '');
            // Accept both array and object for permissions
            let selectedPaths = [];
            if (Array.isArray(roleData.permissions)) {
                selectedPaths = roleData.permissions;
            } else if (roleData.permissions && typeof roleData.permissions === 'object') {
                selectedPaths = Object.keys(roleData.permissions);
            }
            setPermissions(selectedPaths);
            permissionsRef.current = selectedPaths;

            // Expand categories with selected permissions
            const categoriesWithPermissions = {};
            PAGE_CATEGORIES.forEach(category => {
                if (category.pages.some(page => selectedPaths.includes(page.path))) {
                    categoriesWithPermissions[category.name] = true;
                }
            });
            setExpandedCategories(categoriesWithPermissions);
        } else {
            setName('');
            setDescription('');
            setPermissions([]);
            setExpandedCategories({});
        }
        setIsSubmitting(false); // Reset submitting state when modal opens
    }, [roleData]);

    const togglePermission = (page) => {
        let updated;
        if (permissions.includes(page)) {
            updated = permissions.filter(p => p !== page);
        } else {
            updated = [...permissions, page];
        }
        permissionsRef.current = updated; // update ref immediately
        setPermissions(updated); // update state
        console.log('Toggled:', page, 'Updated permissions:', updated);
    };

    const hasPermission = (page) => {
        return permissions.includes(page);
    };

    const toggleCategory = (categoryName) => {
        setExpandedCategories(prev => ({
            ...prev,
            [categoryName]: !prev[categoryName]
        }));
    };

    const handleSubmit = async () => {
        if (isSubmitting) return; // Prevent double submit
        if (!name.trim()) {
            alert('Role name is required');
            return;
        }
        setIsSubmitting(true);
        try {
            console.log('Submitting permissions:', permissionsRef.current);
            await onSubmit({
                id: roleData?.id,
                name: name.trim(),
                description: description.trim(),
                permissions: permissionsRef.current,
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const countSelectedPermissions = () => {
        return permissions.length;
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-5xl max-h-[90vh] flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-blue-800 rounded-t-xl">
                    <div className="flex items-center space-x-3">
                        {roleData ? (
                            <>
                                <FiUser className="text-xl text-white" />
                                <h2 className="text-xl font-bold text-white">
                                    Edit Role: {roleData.name}
                                </h2>
                            </>
                        ) : (
                            <>
                                <FiUserPlus className="text-xl text-white" />
                                <h2 className="text-xl font-bold text-white">
                                    Create New Role
                                </h2>
                            </>
                        )}
                    </div>
                    <button
                        onClick={onClose}
                        className="text-white transition-colors hover:text-blue-100"
                    >
                        <FiX className="text-xl" />
                    </button>
                </div>

                {/* Tabs */}
                <div className="border-b border-gray-200">
                    <nav className="flex -mb-px">
                        <button
                            onClick={() => setActiveTab('details')}
                            className={`py-3 px-6 text-center border-b-2 font-medium text-sm flex items-center space-x-2 ${activeTab === 'details'
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            <FiInfo />
                            <span>Details</span>
                        </button>
                        <button
                            onClick={() => setActiveTab('permissions')}
                            className={`py-3 px-6 text-center border-b-2 font-medium text-sm flex items-center space-x-2 ${activeTab === 'permissions'
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            <FiShield />
                            <span>Permissions</span>
                            {countSelectedPermissions() > 0 && (
                                <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-0.5 rounded-full">
                                    {countSelectedPermissions()}
                                </span>
                            )}
                        </button>
                    </nav>
                </div>

                {/* Body */}
                <div className="flex-1 p-6 overflow-y-auto">
                    {activeTab === 'details' ? (
                        <div className="space-y-6">
                            <div>
                                <label className="block mb-1 text-sm font-medium text-gray-700">
                                    Role Name *
                                </label>
                                <div className="relative">
                                    <input
                                        type="text"
                                        value={name}
                                        onChange={(e) => setName(e.target.value)}
                                        className="w-full px-4 py-2 transition-all border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="e.g., Administrator, Manager"
                                        autoFocus
                                        required
                                    />
                                    <FiLock className="absolute right-3 top-2.5 text-gray-400" />
                                </div>
                            </div>

                            <div>
                                <label className="block mb-1 text-sm font-medium text-gray-700">
                                    Description
                                </label>
                                <textarea
                                    value={description}
                                    onChange={(e) => setDescription(e.target.value)}
                                    className="w-full px-4 py-2 transition-all border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Describe the purpose of this role..."
                                    rows={3}
                                />
                            </div>

                            <div className="p-4 border border-blue-100 rounded-lg bg-blue-50">
                                <div className="flex items-start">
                                    <FiInfo className="text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
                                    <p className="text-sm text-blue-700">
                                        After filling out the basic details, switch to the Permissions tab to configure what this role can access.
                                    </p>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="flex items-center text-lg font-semibold text-gray-800">
                                        <FiShield className="mr-2 text-blue-600" />
                                        Page Permissions Configuration
                                    </h3>
                                    <p className="text-sm text-gray-500">
                                        Select the pages this role can access
                                    </p>
                                </div>
                                <div className="px-3 py-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full">
                                    {countSelectedPermissions()} pages selected
                                </div>
                            </div>

                            {/* Permissions List */}
                            <div className="mt-4 space-y-4">
                                {PAGE_CATEGORIES.map(category => (
                                    <div key={category.name} className="overflow-hidden border border-gray-200 rounded-lg">
                                        <button
                                            onClick={() => toggleCategory(category.name)}
                                            className="flex items-center justify-between w-full p-4 transition-colors bg-gray-50 hover:bg-gray-100"
                                        >
                                            <div className="flex items-center space-x-3">
                                                <span className="text-gray-700">{category.icon}</span>
                                                <span className="font-medium text-gray-800">{category.name}</span>
                                            </div>
                                            <FiChevronDown
                                                className={`text-gray-500 transition-transform ${expandedCategories[category.name] ? 'transform rotate-180' : ''}`}
                                            />
                                        </button>

                                        {expandedCategories[category.name] && (
                                            <div className="p-4 bg-white">
                                                {category.pages.map(page => (
                                                    <div key={page.path} className="flex items-center py-2">
                                                        <input
                                                            type="checkbox"
                                                            checked={hasPermission(page.path)}
                                                            onChange={() => togglePermission(page.path)}
                                                            className="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                                        />
                                                        <span className="ml-3 text-gray-900">{page.name}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>

                            <div className="flex items-center mt-4 text-sm text-gray-500">
                                <FiInfo className="mr-2 text-gray-400" />
                                <span>
                                    Check the boxes to grant access to specific pages for this role.
                                </span>
                            </div>
                        </div>
                    )}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-xl">
                    <div className="text-sm text-gray-500">
                        {activeTab === 'permissions' && (
                            <span>{countSelectedPermissions()} pages selected</span>
                        )}
                    </div>
                    <div className="flex space-x-3">
                        <button
                            type="button"
                            onClick={onClose}
                            className="flex items-center px-4 py-2 space-x-2 text-gray-700 transition-colors border border-gray-300 rounded-lg hover:bg-gray-100"
                            disabled={isSubmitting}
                        >
                            <FiX />
                            <span>Cancel</span>
                        </button>
                        {activeTab === 'details' ? (
                            <button
                                type="button"
                                onClick={() => setActiveTab('permissions')}
                                className="flex items-center px-4 py-2 space-x-2 text-white transition-colors bg-gray-800 rounded-lg hover:bg-gray-700"
                                disabled={isSubmitting}
                            >
                                <span>Continue to Permissions</span>
                                <FiCheck />
                            </button>
                        ) : (
                            <button
                                type="button"
                                onClick={handleSubmit}
                                className={`px-4 py-2 rounded-lg text-white flex items-center space-x-2 transition-colors ${!name.trim() || isSubmitting
                                    ? 'bg-gray-400 cursor-not-allowed'
                                    : 'bg-blue-600 hover:bg-blue-700'
                                    }`}
                                disabled={!name.trim() || isSubmitting}
                            >
                                <FiSave />
                                <span>{isSubmitting ? (roleData ? 'Updating...' : 'Creating...') : (roleData ? 'Update Role' : 'Create Role')}</span>
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

RoleModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onSubmit: PropTypes.func.isRequired,
    roleData: PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        name: PropTypes.string,
        description: PropTypes.string,
        permissions: PropTypes.oneOfType([
            PropTypes.objectOf(PropTypes.arrayOf(PropTypes.string)),
            PropTypes.arrayOf(PropTypes.string)
        ]),
    }),
};

export default RoleModal;